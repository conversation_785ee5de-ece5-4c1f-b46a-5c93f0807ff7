import { world, system, GameMode, EntityComponentTypes } from '@minecraft/server';
/**
 * Dynamic Hearts Add-on
 * Players start with 1 heart and gain 1 heart each time they die, up to 20 hearts max.
 * At 20 hearts, death results in spectator mode (hardcore elimination).
 */
const HEART_PROPERTY = 'eabidakmusic:heart_level';
const ELIMINATED_PROPERTY = 'eabidakmusic:eliminated';
const MIN_HEARTS = 1;
const MAX_HEARTS = 20;
/**
 * Initialize a new player with 1 heart
 */
function initializeNewPlayer(player) {
    try {
        // Set heart level property to 1
        player.setProperty(HEART_PROPERTY, MIN_HEARTS);
        // Apply the 1-heart component group
        player.triggerEvent('eabidakmusic:set_hearts_1');
        // Set current health to 2 (1 heart = 2 health points)
        system.runTimeout(() => {
            const healthComponent = player.getComponent(EntityComponentTypes.Health);
            if (healthComponent) {
                healthComponent.setCurrentValue(2);
            }
        }, 5); // Small delay to ensure component group is applied
        player.sendMessage('§6Welcome! You start with 1 heart. Each death will grant you an additional heart (max 20).');
    }
    catch (error) {
        console.warn(`Failed to initialize player ${player.name}: ${error}`);
    }
}
/**
 * Handle player death and heart progression
 */
function handlePlayerDeath(player) {
    try {
        const currentHearts = player.getProperty(HEART_PROPERTY) || MIN_HEARTS;
        if (currentHearts >= MAX_HEARTS) {
            // Player died at max hearts - eliminate them
            eliminatePlayer(player);
            return;
        }
        // Increment heart level
        const newHearts = Math.min(currentHearts + 1, MAX_HEARTS);
        player.setProperty(HEART_PROPERTY, newHearts);
        // Send progression message
        player.sendMessage(`§a+1 Heart! You now have ${newHearts} heart${newHearts > 1 ? 's' : ''}.`);
        if (newHearts === MAX_HEARTS) {
            player.sendMessage('§6§lMAXIMUM HEARTS REACHED! §r§6Next death will eliminate you!');
        }
    }
    catch (error) {
        console.warn(`Failed to handle death for player ${player.name}: ${error}`);
    }
}
/**
 * Eliminate a player (set to spectator mode and mark as eliminated)
 */
function eliminatePlayer(player) {
    try {
        // Mark player as eliminated
        player.setProperty(ELIMINATED_PROPERTY, true);
        // Switch to spectator mode
        player.setGameMode(GameMode.spectator);
        // Announce elimination
        world.sendMessage(`§c${player.name} has been eliminated! (Died with maximum hearts)`);
        player.sendMessage('§c§lYOU HAVE BEEN ELIMINATED! §r§cYou are now in spectator mode.');
    }
    catch (error) {
        console.warn(`Failed to eliminate player ${player.name}: ${error}`);
    }
}
/**
 * Check if a player is eliminated
 */
function isPlayerEliminated(player) {
    const eliminated = player.getProperty(ELIMINATED_PROPERTY);
    return eliminated === true;
}
/**
 * Apply the correct heart level to a player on spawn/respawn
 */
function applyHeartLevel(player) {
    try {
        const heartLevel = player.getProperty(HEART_PROPERTY) || MIN_HEARTS;
        const clampedHearts = Math.max(MIN_HEARTS, Math.min(MAX_HEARTS, heartLevel));
        // Trigger the appropriate component group event
        const eventName = `eabidakmusic:set_hearts_${clampedHearts}`;
        player.triggerEvent(eventName);
        // Set current health to maximum for this heart level
        system.runTimeout(() => {
            const healthComponent = player.getComponent(EntityComponentTypes.Health);
            if (healthComponent) {
                healthComponent.resetToMaxValue();
            }
        }, 5); // Small delay to ensure component group is applied
    }
    catch (error) {
        console.warn(`Failed to apply heart level for player ${player.name}: ${error}`);
    }
}
/**
 * Main initialization function
 */
function initializeDynamicHearts() {
    console.log('Dynamic Hearts Add-on initialized');
    // Handle player spawn events (includes respawn)
    world.afterEvents.playerSpawn.subscribe((event) => {
        const player = event.player;
        // Anti-cheat: Check if player is eliminated first
        const isEliminated = isPlayerEliminated(player);
        if (isEliminated) {
            // Eliminated player is spawning - force them back to spectator mode
            system.runTimeout(() => {
                try {
                    player.setGameMode(GameMode.spectator);
                    player.sendMessage('§c§lANTI-CHEAT: §r§cYou are eliminated and must remain in spectator mode!');
                    console.warn(`Anti-cheat: Eliminated player ${player.name} tried to spawn, forced back to spectator`);
                }
                catch (error) {
                    console.warn(`Failed to enforce spectator mode for eliminated player ${player.name} on spawn: ${error}`);
                }
            }, 5); // Small delay to ensure spawn is complete
            return;
        }
        // Check if this is a new player (no heart property set)
        const heartLevel = player.getProperty(HEART_PROPERTY);
        if (heartLevel === undefined || typeof heartLevel !== 'number') {
            // New player - initialize with 1 heart
            initializeNewPlayer(player);
        }
        else {
            // Existing player respawning - apply their current heart level
            applyHeartLevel(player);
        }
    });
    // Handle player death events
    world.afterEvents.entityDie.subscribe((event) => {
        if (event.deadEntity.typeId === 'minecraft:player') {
            const player = event.deadEntity;
            handlePlayerDeath(player);
        }
    });
    // Handle gamemode changes (anti-cheat for eliminated players)
    world.afterEvents.playerGameModeChange.subscribe((event) => {
        const player = event.player;
        const newGameMode = event.toGameMode;
        const oldGameMode = event.fromGameMode;
        // Check if player is eliminated and trying to escape spectator mode
        const isEliminated = isPlayerEliminated(player);
        const heartLevel = player.getProperty(HEART_PROPERTY) || MIN_HEARTS;
        if (isEliminated && newGameMode !== GameMode.spectator) {
            // Eliminated player is trying to escape spectator mode - force them back
            system.runTimeout(() => {
                try {
                    player.setGameMode(GameMode.spectator);
                    player.sendMessage('§c§lANTI-CHEAT: §r§cYou have been eliminated and cannot change gamemode!');
                    world.sendMessage(`§c${player.name} attempted to escape elimination but was caught by anti-cheat!`);
                    console.warn(`Anti-cheat triggered: ${player.name} tried to change from ${oldGameMode} to ${newGameMode} while eliminated`);
                }
                catch (error) {
                    console.warn(`Failed to enforce spectator mode for eliminated player ${player.name}: ${error}`);
                }
            }, 1); // Very short delay to ensure the gamemode change is processed first
        }
        // Additional check: if player has max hearts but isn't marked as eliminated, check their status
        if (heartLevel >= MAX_HEARTS && !isEliminated && newGameMode !== GameMode.spectator) {
            // This could happen if the elimination property wasn't set properly
            console.warn(`Player ${player.name} has max hearts but isn't marked as eliminated. Checking status...`);
            // Re-eliminate them to be safe
            system.runTimeout(() => {
                eliminatePlayer(player);
            }, 5);
        }
    });
    // Initialize existing players when add-on loads
    system.runTimeout(() => {
        for (const player of world.getAllPlayers()) {
            const heartLevel = player.getProperty(HEART_PROPERTY);
            if (heartLevel === undefined || typeof heartLevel !== 'number') {
                initializeNewPlayer(player);
            }
            else {
                applyHeartLevel(player);
            }
        }
    }, 20); // Wait 1 second for world to fully load
    // Periodic anti-cheat check (every 30 seconds)
    system.runInterval(() => {
        try {
            for (const player of world.getAllPlayers()) {
                const isEliminated = isPlayerEliminated(player);
                const currentGameMode = player.getGameMode();
                // Check if eliminated player is not in spectator mode
                if (isEliminated && currentGameMode !== GameMode.spectator) {
                    player.setGameMode(GameMode.spectator);
                    player.sendMessage('§c§lANTI-CHEAT: §r§cYou are eliminated and must remain in spectator mode!');
                    world.sendMessage(`§c${player.name} was caught trying to escape elimination!`);
                    console.warn(`Periodic anti-cheat: Forced ${player.name} back to spectator mode`);
                }
                // Check for players with max hearts who aren't marked as eliminated
                const heartLevel = player.getProperty(HEART_PROPERTY) || MIN_HEARTS;
                if (heartLevel >= MAX_HEARTS && !isEliminated && currentGameMode !== GameMode.spectator) {
                    console.warn(`Periodic anti-cheat: Player ${player.name} has max hearts but isn't eliminated. Fixing...`);
                    eliminatePlayer(player);
                }
            }
        }
        catch (error) {
            console.warn(`Error in periodic anti-cheat check: ${error}`);
        }
    }, 600); // Run every 30 seconds (600 ticks)
}
// Start the add-on
system.run(initializeDynamicHearts);
