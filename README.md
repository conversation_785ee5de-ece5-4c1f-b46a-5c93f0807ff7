# Dynamic Hearts - Minecraft Bedrock Add-On

A comprehensive Minecraft Bedrock Edition add-on that implements a progressive heart system where players start with 1 heart and gain additional hearts upon death, up to a maximum of 20 hearts. Players who die at 20 hearts are switched to spectator mode for hardcore-style elimination.

## Features

### Core Mechanics
- **Progressive Heart System**: Players start with exactly 1 heart (2 health points)
- **Death Progression**: Upon death, players respawn with +1 additional heart
- **Maximum Limit**: Players can have up to 20 hearts (40 health points)
- **Hardcore Elimination**: Players who die at 20 hearts are automatically switched to spectator mode
- **Persistent Storage**: Player heart counts are saved using dynamic properties and persist across sessions

### Technical Features
- **Entity Properties**: Uses modern entity properties to track heart levels (1-20) and elimination status
- **Component Groups**: Uses component groups with `minecraft:health` components for each heart level
- **Event-Driven**: Uses `world.afterEvents.playerSpawn` and `world.afterEvents.entityDie` for progression
- **Health Management**: Direct health control using `healthComponent.setCurrentValue()` and `resetToMaxValue()`
- **Anti-Cheat System**: Comprehensive anti-cheat protection against gamemode manipulation
- **Multiplayer Support**: Fully functional in multiplayer environments
- **Error Handling**: Comprehensive error handling and recovery systems
- **TypeScript**: Full TypeScript implementation with strict type safety

## Installation

1. **Download**: Download the behavior pack from the releases section
2. **Install**: Place the behavior pack in your Minecraft worlds' `behavior_packs` folder
3. **Activate**: Enable the behavior pack in your world settings
4. **Requirements**: Ensure your world has "Holiday Creator Features" experimental toggle enabled

## File Structure

```
├── src/eabidakmusic/dynamic_hearts/
│   └── main.ts                           # Main TypeScript logic
├── packs/BP/
│   ├── entities/eabidakmusic/dynamic_hearts/
│   │   └── player.e.bp.json             # Player entity with heart component groups
│   ├── scripts/eabidakmusic/dynamic_hearts/
│   │   └── main.js                      # Compiled JavaScript (auto-generated)
│   ├── texts/
│   │   ├── en_US.lang                   # Language file
│   │   └── languages.json               # Language configuration
│   └── manifest.json                    # Behavior pack manifest
├── tsconfig.json                        # TypeScript configuration
└── package.json                         # Node.js dependencies
```

## How It Works

### Player Initialization
- New players automatically start with 1 heart when they first join
- Existing players maintain their current heart count when the add-on is activated
- Player data is stored using Minecraft's dynamic properties system

### Death and Respawn Cycle
1. Player dies with current heart count
2. System increases heart count by 1 (up to maximum of 20)
3. Player respawns with new heart count
4. Health is automatically set to match the new heart count

### Health Enforcement
- Event-driven monitoring using `world.afterEvents.entityHealthChanged` for immediate response
- Uses Health Boost effects to increase maximum health beyond default 10 hearts
- Instantly sets health to current maximum using `healthComponent.setCurrentValue()` when exceeded
- Direct health control prevents healing beyond designated heart limits through any means
- Persistent maximum health through dynamic properties and effect restoration on rejoin

### Elimination System
- Players who die while at 20 hearts are eliminated
- Eliminated players are switched to spectator mode
- Elimination status is persistent across sessions

### Anti-Cheat System
The add-on includes a comprehensive anti-cheat system to prevent eliminated players from escaping spectator mode:

#### Real-time Protection
- **Gamemode Change Detection**: Monitors `world.afterEvents.playerGameModeChange` to catch attempts to escape spectator mode
- **Spawn Protection**: Checks elimination status during `world.afterEvents.playerSpawn` to prevent respawn exploitation
- **Immediate Enforcement**: Forces eliminated players back to spectator mode within 1-5 ticks of detection

#### Periodic Monitoring
- **30-Second Sweeps**: Runs comprehensive checks every 30 seconds to catch any bypassed attempts
- **Status Validation**: Verifies that players with max hearts are properly marked as eliminated
- **Automatic Correction**: Fixes any inconsistencies in player status automatically

#### Multi-Layer Detection
1. **Property-Based Tracking**: Uses `eabidakmusic:eliminated` property for persistent elimination status
2. **Heart Level Verification**: Cross-references heart count with elimination status
3. **Gamemode Validation**: Ensures eliminated players remain in spectator mode
4. **Rejoin Protection**: Prevents elimination escape through world rejoin

#### Anti-Cheat Messages
- Players receive clear warnings when anti-cheat is triggered
- Server announcements alert other players to escape attempts
- Console logging provides detailed information for administrators

## Configuration

### System Constants (constants.ts)
```typescript
// Core gameplay settings
MIN_HEARTS: 1,              // Starting hearts
MAX_HEARTS: 20,             // Maximum hearts before elimination
HEALTH_PER_HEART: 2,        // Minecraft standard (2 HP per heart)

// Performance settings
MAINTENANCE_INTERVAL: 1200,  // System maintenance frequency (ticks)
SPAWN_DELAY: 40,            // Delay before applying changes (ticks)
```

### Customization
To modify the system behavior:
1. Edit values in `constants.ts`
2. Recompile TypeScript to JavaScript
3. Test thoroughly in a development world

## API Reference

### Core Classes

#### `DynamicHeartsSystem`
Main system coordinator that manages all components.

#### `PlayerManager`
Handles player-specific operations:
- `initializePlayer(player, isFirstJoin)` - Initialize a player in the system
- `increasePlayerHearts(player)` - Increase player hearts on death
- `eliminatePlayer(player)` - Switch player to spectator mode
- `getPlayerData(player)` - Retrieve player heart data

#### `HealthMonitor`
Monitors and enforces health limits using event-driven approach:
- `initializeEventMonitoring()` - Set up health change event listeners
- `monitorPlayer(player)` - Check specific player's health when events occur
- `enforceHealthLimits(monitorData)` - Apply health corrections immediately

#### `EventHandlers`
Manages game event subscriptions:
- Player join/leave events
- Player spawn/death events
- Automatic cleanup and initialization

## Performance

### Optimization Features
- **Event-Driven Processing**: Health monitoring only triggers when health actually changes
- **Efficient Caching**: Player data cached in memory for fast access
- **Smart Monitoring**: Immediate response to health changes using `world.afterEvents.entityHealthChanged`
- **Performance Metrics**: Built-in performance monitoring and event tracking

### Performance Metrics
- Average event processing time tracking
- Slow operation detection and logging
- Health change event counting
- Memory usage monitoring
- Automatic performance optimization

### Maximum Health Management
- **Health Boost Effects**: Uses `minecraft:health_boost` effect to increase maximum health beyond default 10 hearts
- **Effect Persistence**: Health Boost effects are automatically restored when players rejoin the world
- **Dynamic Properties**: Heart count is saved to player dynamic properties for persistence across sessions
- **Automatic Restoration**: Maximum health is restored on player join and spawn events
- **Seamless Experience**: Players maintain their heart progression without manual intervention

## Troubleshooting

### Common Issues

**Players not starting with 1 heart:**
- Ensure the add-on is properly activated
- Check that experimental features are enabled
- Verify the player hasn't been initialized before

**Health not being enforced:**
- Check console for error messages
- Ensure players are in survival/adventure mode
- Verify the health monitoring system is running

**Players not gaining hearts on death:**
- Check that death events are being detected
- Verify player data is being saved properly
- Ensure the player isn't already at maximum hearts

### Debug Mode
Enable debug mode by modifying the system initialization:
```typescript
const dynamicHeartsSystem = new DynamicHeartsSystem(true); // Enable debug
```

Debug mode provides:
- Detailed console logging
- Performance metrics
- Event tracking
- Error stack traces

## Compatibility

### Minecraft Versions
- **Minimum**: Minecraft Bedrock 1.21.80+
- **Recommended**: Latest stable version
- **API Version**: @minecraft/server 1.19.0+

### Multiplayer Support
- Fully compatible with multiplayer worlds
- Supports dedicated servers
- Player data synced across sessions
- No client-side modifications required

## Development

### Building from Source
1. Install Node.js and TypeScript
2. Run `tsc` to compile TypeScript files
3. Copy compiled JavaScript to behavior pack
4. Test in development world

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make changes with proper TypeScript typing
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Credits

- **Authors**: EabidakMusic, Raboy13
- **Version**: 1.0.0
- **Built with**: TypeScript, @minecraft/server API
- **Documentation**: Context7 Minecraft Bedrock API references

## Support

For support, bug reports, or feature requests:
1. Check the troubleshooting section
2. Enable debug mode for detailed logs
3. Report issues with console output
4. Include world settings and add-on configuration
