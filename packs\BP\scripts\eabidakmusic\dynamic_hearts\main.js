import { world, system, GameMode, EntityComponentTypes } from '@minecraft/server';
/**
 * Dynamic Hearts Add-on
 * Players start with 1 heart and gain 1 heart each time they die, up to 20 hearts max.
 * At 20 hearts, death results in spectator mode (hardcore elimination).
 */
const HEART_PROPERTY = 'eabidakmusic:heart_level';
const MIN_HEARTS = 1;
const MAX_HEARTS = 20;
/**
 * Initialize a new player with 1 heart
 */
function initializeNewPlayer(player) {
    try {
        // Set heart level property to 1
        player.setProperty(HEART_PROPERTY, MIN_HEARTS);
        // Apply the 1-heart component group
        player.triggerEvent('eabidakmusic:set_hearts_1');
        // Set current health to 2 (1 heart = 2 health points)
        system.runTimeout(() => {
            const healthComponent = player.getComponent(EntityComponentTypes.Health);
            if (healthComponent) {
                healthComponent.setCurrentValue(2);
            }
        }, 5); // Small delay to ensure component group is applied
        player.sendMessage('§6Welcome! You start with 1 heart. Each death will grant you an additional heart (max 20).');
    }
    catch (error) {
        console.warn(`Failed to initialize player ${player.name}: ${error}`);
    }
}
/**
 * Handle player death and heart progression
 */
function handlePlayerDeath(player) {
    try {
        const currentHearts = player.getProperty(HEART_PROPERTY) || MIN_HEARTS;
        if (currentHearts >= MAX_HEARTS) {
            // Player died at max hearts - switch to spectator mode
            player.setGameMode(GameMode.spectator);
            world.sendMessage(`§c${player.name} has been eliminated! (Died with maximum hearts)`);
            return;
        }
        // Increment heart level
        const newHearts = Math.min(currentHearts + 1, MAX_HEARTS);
        player.setProperty(HEART_PROPERTY, newHearts);
        // Send progression message
        player.sendMessage(`§a+1 Heart! You now have ${newHearts} heart${newHearts > 1 ? 's' : ''}.`);
        if (newHearts === MAX_HEARTS) {
            player.sendMessage('§6§lMAXIMUM HEARTS REACHED! §r§6Next death will eliminate you!');
        }
    }
    catch (error) {
        console.warn(`Failed to handle death for player ${player.name}: ${error}`);
    }
}
/**
 * Apply the correct heart level to a player on spawn/respawn
 */
function applyHeartLevel(player) {
    try {
        const heartLevel = player.getProperty(HEART_PROPERTY) || MIN_HEARTS;
        const clampedHearts = Math.max(MIN_HEARTS, Math.min(MAX_HEARTS, heartLevel));
        // Trigger the appropriate component group event
        const eventName = `eabidakmusic:set_hearts_${clampedHearts}`;
        player.triggerEvent(eventName);
        // Set current health to maximum for this heart level
        system.runTimeout(() => {
            const healthComponent = player.getComponent(EntityComponentTypes.Health);
            if (healthComponent) {
                healthComponent.resetToMaxValue();
            }
        }, 5); // Small delay to ensure component group is applied
    }
    catch (error) {
        console.warn(`Failed to apply heart level for player ${player.name}: ${error}`);
    }
}
/**
 * Main initialization function
 */
function initializeDynamicHearts() {
    console.log('Dynamic Hearts Add-on initialized');
    // Handle player spawn events (includes respawn)
    world.afterEvents.playerSpawn.subscribe((event) => {
        const player = event.player;
        // Check if this is a new player (no heart property set)
        const heartLevel = player.getProperty(HEART_PROPERTY);
        if (heartLevel === undefined || typeof heartLevel !== 'number') {
            // New player - initialize with 1 heart
            initializeNewPlayer(player);
        }
        else {
            // Existing player respawning - apply their current heart level
            applyHeartLevel(player);
        }
    });
    // Handle player death events
    world.afterEvents.entityDie.subscribe((event) => {
        if (event.deadEntity.typeId === 'minecraft:player') {
            const player = event.deadEntity;
            handlePlayerDeath(player);
        }
    });
    // Initialize existing players when add-on loads
    system.runTimeout(() => {
        for (const player of world.getAllPlayers()) {
            const heartLevel = player.getProperty(HEART_PROPERTY);
            if (heartLevel === undefined || typeof heartLevel !== 'number') {
                initializeNewPlayer(player);
            }
            else {
                applyHeartLevel(player);
            }
        }
    }, 20); // Wait 1 second for world to fully load
}
// Start the add-on
system.run(initializeDynamicHearts);
